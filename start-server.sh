#!/bin/bash

echo "🚀 Iniciando SmartTV Server..."

# Definir variáveis de ambiente
export QT_QPA_PLATFORM=offscreen

# Função para matar processos na porta
kill_port() {
    local port=$1
    echo "🔍 Verificando porta $port..."
    
    # Encontrar e matar processos na porta
    local pids=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pids" ]; then
        echo "💀 Matando processos na porta $port: $pids"
        kill -9 $pids 2>/dev/null
        sleep 2
    fi
}

# Matar processos nas portas
kill_port 3001
kill_port 5173

echo "🧹 Limpando processos antigos..."
sleep 3

# Ir para o diretório do projeto
cd /www/wwwroot/smarttv

echo "📦 Instalando dependências se necessário..."
npm install --silent

echo "🔧 Iniciando Backend (Porta 3001)..."
cd /www/wwwroot/smarttv/api
nohup node proxy-server.cjs > ../backend.log 2>&1 &
BACKEND_PID=$!

echo "⏳ Aguardando backend inicializar..."
sleep 5

echo "🎨 Iniciando Frontend (Porta 5173)..."
cd /www/wwwroot/smarttv
nohup npm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

echo "⏳ Aguardando frontend inicializar..."
sleep 8

echo ""
echo "✅ SERVIDORES INICIADOS COM SUCESSO!"
echo ""
echo "🌐 Frontend: https://smartv.shop"
echo "🔧 Backend:  http://localhost:3001"
echo ""
echo "📊 PIDs:"
echo "   Backend:  $BACKEND_PID"
echo "   Frontend: $FRONTEND_PID"
echo ""
echo "📝 Logs:"
echo "   Backend:  tail -f /www/wwwroot/smarttv/backend.log"
echo "   Frontend: tail -f /www/wwwroot/smarttv/frontend.log"
echo ""
echo "🛑 Para parar os servidores:"
echo "   kill $BACKEND_PID $FRONTEND_PID"
echo ""
