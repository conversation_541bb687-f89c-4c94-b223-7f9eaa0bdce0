import { registerHandlers, run, type Handler } from "encore.dev/internal/codegen/appinit";
import { Worker, isMainThread } from "node:worker_threads";
import { fileURLToPath } from "node:url";
import { availableParallelism } from "node:os";

import { sendReceipt as sendReceiptImpl0 } from "../../../../../whatsapp\\send-receipt";
import { sendAdminNotification as sendAdminNotificationImpl1 } from "../../../../../whatsapp\\send-receipt";
import * as whatsapp_service from "../../../../../whatsapp\\encore.service";

const handlers: Handler[] = [
    {
        apiRoute: {
            service:           "whatsapp",
            name:              "sendReceipt",
            handler:           sendReceiptImpl0,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: whatsapp_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "whatsapp",
            name:              "sendAdminNotification",
            handler:           sendAdminNotificationImpl1,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: whatsapp_service.default.cfg.middlewares || [],
    },
];

registerHandlers(handlers);

await run(import.meta.url);
