import { CallOpts } from "encore.dev/api";

type Parameters<T> = T extends (...args: infer P) => unknown ? P : never;
type WithCallOpts<T extends (...args: any) => any> = (
  ...args: [...Parameters<T>, opts?: CallOpts]
) => ReturnType<T>;

import { requestTest as requestTest_handler } from "../../../../test\\test-api.js";
declare const requestTest: WithCallOpts<typeof requestTest_handler>;
export { requestTest };


