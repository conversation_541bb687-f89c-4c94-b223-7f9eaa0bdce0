import { CallOpts } from "encore.dev/api";

type Parameters<T> = T extends (...args: infer P) => unknown ? P : never;
type WithCallOpts<T extends (...args: any) => any> = (
  ...args: [...Parameters<T>, opts?: CallOpts]
) => ReturnType<T>;

import { sendReceipt as sendReceipt_handler } from "../../../../whatsapp\\send-receipt.js";
declare const sendReceipt: WithCallOpts<typeof sendReceipt_handler>;
export { sendReceipt };

import { sendAdminNotification as sendAdminNotification_handler } from "../../../../whatsapp\\send-receipt.js";
declare const sendAdminNotification: WithCallOpts<typeof sendAdminNotification_handler>;
export { sendAdminNotification };


