#!/bin/bash

echo "🛑 Parando SmartTV Server..."

# Função para matar processos na porta
kill_port() {
    local port=$1
    echo "🔍 Verificando porta $port..."
    
    # Encontrar e matar processos na porta
    local pids=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pids" ]; then
        echo "💀 Matando processos na porta $port: $pids"
        kill -9 $pids 2>/dev/null
        sleep 1
    else
        echo "✅ Porta $port já está livre"
    fi
}

# Matar processos específicos
echo "🧹 Limpando processos Node.js..."
pkill -f "node proxy-server.cjs" 2>/dev/null
pkill -f "vite --host smartv.shop" 2>/dev/null
pkill -f "npm run dev" 2>/dev/null

# Matar processos nas portas
kill_port 3001
kill_port 5173

echo "⏳ Aguardando processos finalizarem..."
sleep 3

echo "✅ Todos os servidores foram parados!"
echo ""
