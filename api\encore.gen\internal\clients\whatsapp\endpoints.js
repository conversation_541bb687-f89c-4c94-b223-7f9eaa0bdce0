import { apiCall, streamIn, streamOut, streamInOut } from "encore.dev/internal/codegen/api";

const TEST_ENDPOINTS = typeof ENCORE_DROP_TESTS === "undefined" && process.env.NODE_ENV === "test"
    ? await import("./endpoints_testing.js")
    : null;

export async function sendReceipt(params, opts) {
    if (typeof ENCORE_DROP_TESTS === "undefined" && process.env.NODE_ENV === "test") {
        return TEST_ENDPOINTS.sendReceipt(params, opts);
    }

    return apiCall("whatsapp", "sendReceipt", params, opts);
}
export async function sendAdminNotification(params, opts) {
    if (typeof ENCORE_DROP_TESTS === "undefined" && process.env.NODE_ENV === "test") {
        return TEST_ENDPOINTS.sendAdminNotification(params, opts);
    }

    return apiCall("whatsapp", "sendAdminNotification", params, opts);
}
