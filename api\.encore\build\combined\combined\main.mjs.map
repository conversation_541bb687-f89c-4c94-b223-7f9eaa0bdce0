{"version": 3, "sources": ["../../../../encore.gen/internal/entrypoints/combined/main.ts", "../../../../payment/check-payment.ts", "../../../../payment/create-pix.ts", "../../../../test/test-api.ts", "../../../../whatsapp/send-receipt.ts", "../../../../payment/encore.service.ts", "../../../../whatsapp/encore.service.ts", "../../../../test/encore.service.ts"], "sourcesContent": ["import { registerGateways, registerHandlers, run, type Handler } from \"encore.dev/internal/codegen/appinit\";\n\nimport { checkPayment as payment_checkPaymentImpl0 } from \"../../../../payment\\\\check-payment\";\nimport { createPix as payment_createPixImpl1 } from \"../../../../payment\\\\create-pix\";\nimport { requestTest as test_requestTestImpl2 } from \"../../../../test\\\\test-api\";\nimport { sendReceipt as whatsapp_sendReceiptImpl3 } from \"../../../../whatsapp\\\\send-receipt\";\nimport { sendAdminNotification as whatsapp_sendAdminNotificationImpl4 } from \"../../../../whatsapp\\\\send-receipt\";\nimport * as payment_service from \"../../../../payment\\\\encore.service\";\nimport * as whatsapp_service from \"../../../../whatsapp\\\\encore.service\";\nimport * as frontend_service from \"../../../../frontend\\\\encore.service\";\nimport * as test_service from \"../../../../test\\\\encore.service\";\n\nconst gateways: any[] = [\n];\n\nconst handlers: Handler[] = [\n    {\n        apiRoute: {\n            service:           \"payment\",\n            name:              \"checkPayment\",\n            handler:           payment_checkPaymentImpl0,\n            raw:               false,\n            streamingRequest:  false,\n            streamingResponse: false,\n        },\n        endpointOptions: {\"expose\":true,\"auth\":false,\"isRaw\":false,\"isStream\":false,\"tags\":[]},\n        middlewares: payment_service.default.cfg.middlewares || [],\n    },\n    {\n        apiRoute: {\n            service:           \"payment\",\n            name:              \"createPix\",\n            handler:           payment_createPixImpl1,\n            raw:               false,\n            streamingRequest:  false,\n            streamingResponse: false,\n        },\n        endpointOptions: {\"expose\":true,\"auth\":false,\"isRaw\":false,\"isStream\":false,\"tags\":[]},\n        middlewares: payment_service.default.cfg.middlewares || [],\n    },\n    {\n        apiRoute: {\n            service:           \"test\",\n            name:              \"requestTest\",\n            handler:           test_requestTestImpl2,\n            raw:               false,\n            streamingRequest:  false,\n            streamingResponse: false,\n        },\n        endpointOptions: {\"expose\":true,\"auth\":false,\"isRaw\":false,\"isStream\":false,\"tags\":[]},\n        middlewares: test_service.default.cfg.middlewares || [],\n    },\n    {\n        apiRoute: {\n            service:           \"whatsapp\",\n            name:              \"sendReceipt\",\n            handler:           whatsapp_sendReceiptImpl3,\n            raw:               false,\n            streamingRequest:  false,\n            streamingResponse: false,\n        },\n        endpointOptions: {\"expose\":true,\"auth\":false,\"isRaw\":false,\"isStream\":false,\"tags\":[]},\n        middlewares: whatsapp_service.default.cfg.middlewares || [],\n    },\n    {\n        apiRoute: {\n            service:           \"whatsapp\",\n            name:              \"sendAdminNotification\",\n            handler:           whatsapp_sendAdminNotificationImpl4,\n            raw:               false,\n            streamingRequest:  false,\n            streamingResponse: false,\n        },\n        endpointOptions: {\"expose\":true,\"auth\":false,\"isRaw\":false,\"isStream\":false,\"tags\":[]},\n        middlewares: whatsapp_service.default.cfg.middlewares || [],\n    },\n];\n\nregisterGateways(gateways);\nregisterHandlers(handlers);\n\nawait run(import.meta.url);\n", "import { api, APIError } from \"encore.dev/api\";\nimport { secret } from \"encore.dev/config\";\n\nconst mercadoPagoAccessToken = secret(\"MercadoPagoAccessToken\");\n\nexport interface CheckPaymentRequest {\n  paymentId: string;\n}\n\nexport interface PaymentStatus {\n  id: string;\n  status: string;\n  paid: boolean;\n  amount: number;\n  paidAt?: string;\n}\n\n// Checks the status of a PIX payment using Mercado Pago\nexport const checkPayment = api<CheckPaymentRequest, PaymentStatus>(\n  { expose: true, method: \"GET\", path: \"/payment/:paymentId/status\" },\n  async (req) => {\n    if (!req.paymentId) {\n      throw APIError.invalidArgument(\"Payment ID is required\");\n    }\n\n    // Get Mercado Pago credentials\n    const accessToken = getMercadoPagoAccessToken();\n\n    try {\n      // Get payment status from Mercado Pago API directly\n      const response = await fetch(`https://api.mercadopago.com/v1/payments/${req.paymentId}`, {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${accessToken}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        if (response.status === 404) {\n          throw APIError.notFound(\"Payment not found\");\n        }\n        if (response.status === 401) {\n          throw APIError.unauthenticated(\"API key inválida ou expirada\");\n        }\n        const errorData = await response.text();\n        throw APIError.internal(`HTTP ${response.status}: ${errorData}`);\n      }\n\n      const paymentResponse = await response.json() as any;\n\n      console.log('Payment status response:', JSON.stringify(paymentResponse, null, 2));\n\n      return {\n        id: paymentResponse.id?.toString() || req.paymentId,\n        status: paymentResponse.status || 'unknown',\n        paid: paymentResponse.status === \"approved\",\n        amount: paymentResponse.transaction_amount || 0,\n        paidAt: paymentResponse.date_approved || undefined\n      };\n\n    } catch (err) {\n      console.error('Error checking payment status:', err);\n      \n      // Handle specific Mercado Pago errors\n      const errorMessage = err instanceof Error ? err.message : String(err);\n      if (errorMessage.includes('404') || errorMessage.includes('not found')) {\n        throw APIError.notFound(\"Payment not found\");\n      }\n      if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {\n        throw APIError.unauthenticated(\"API key inválida ou expirada\");\n      }\n      \n      throw APIError.internal(\"Failed to check payment status: \" + errorMessage);\n    }\n  }\n);\n\n// Function to get Mercado Pago Access Token with fallback\nfunction getMercadoPagoAccessToken(): string {\n  try {\n    const token = mercadoPagoAccessToken();\n    if (token && token.trim()) {\n      return token.trim();\n    }\n  } catch (error) {\n    console.log('Secret MercadoPagoAccessToken not found, using fallback');\n  }\n  \n  // Fallback to the provided test token\n  return 'TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072';\n}\n", "import { api, APIError } from \"encore.dev/api\";\nimport { secret } from \"encore.dev/config\";\n\nconst mercadoPagoAccessToken = secret(\"MercadoPagoAccessToken\");\n\nexport interface CreatePixRequest {\n  customerName: string;\n  customerEmail: string;\n  customerDocument: string;\n  customerPhone: string;\n  planType: string;\n  amount: number;\n}\n\nexport interface PixResponse {\n  id: string;\n  qrCode: string;\n  qrCodeUrl: string;\n  pixKey: string;\n  amount: number;\n  expiresAt: string;\n  status: string;\n  transactionAmount: number;\n  description: string;\n}\n\n// Creates a PIX payment for TV channel subscription using Mercado Pago\nexport const createPix = api<CreatePixRequest, PixResponse>(\n  { expose: true, method: \"POST\", path: \"/payment/pix\" },\n  async (req) => {\n    // Validate required fields\n    if (!req.customerName || !req.customerEmail || !req.customerDocument || !req.planType) {\n      throw APIError.invalidArgument(\"Missing required fields\");\n    }\n\n    // Validate document (CPF/CNPJ)\n    const document = req.customerDocument.replace(/\\D/g, \"\");\n    if (!validateDocument(document)) {\n      throw APIError.invalidArgument(\"Invalid document number\");\n    }\n\n    // Validate amount\n    if (req.amount <= 0) {\n      throw APIError.invalidArgument(\"Invalid amount\");\n    }\n\n    // Get Mercado Pago credentials\n    const accessToken = getMercadoPagoAccessToken();\n    \n    console.log('Using Mercado Pago Access Token (first 20 chars):', accessToken.substring(0, 20) + '...');\n\n    try {\n      // Create PIX payment using Mercado Pago API directly\n      const paymentData = {\n        transaction_amount: req.amount,\n        description: `SmartV IPTV - Plano ${req.planType}`,\n        payment_method_id: 'pix',\n        payer: {\n          email: req.customerEmail,\n          first_name: req.customerName.split(' ')[0],\n          last_name: req.customerName.split(' ').slice(1).join(' ') || req.customerName.split(' ')[0],\n          identification: {\n            type: document.length === 11 ? 'CPF' : 'CNPJ',\n            number: document\n          }\n        },\n        external_reference: `SMARTV_${req.planType}_${Date.now()}`,\n        notification_url: 'https://your-domain.com/webhook/mercadopago',\n        date_of_expiration: new Date(Date.now() + 15 * 60 * 1000).toISOString()\n      };\n\n      console.log('Creating Mercado Pago payment with data:', JSON.stringify(paymentData, null, 2));\n\n      const response = await fetch('https://api.mercadopago.com/v1/payments', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${accessToken}`,\n          'X-Idempotency-Key': `SMARTV_${req.planType}_${Date.now()}`\n        },\n        body: JSON.stringify(paymentData)\n      });\n\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Mercado Pago API error:', response.status, errorData);\n        throw new Error(`Mercado Pago API error: ${response.status} - ${errorData}`);\n      }\n\n      const paymentResponse = await response.json() as any;\n      \n      console.log('Mercado Pago payment created successfully:', paymentResponse.id);\n      console.log('Payment response:', JSON.stringify(paymentResponse, null, 2));\n\n      if (!paymentResponse.id) {\n        throw new Error(\"Failed to create Mercado Pago payment\");\n      }\n\n      // Extract PIX information\n      const pixInfo = paymentResponse.point_of_interaction?.transaction_data;\n      \n      if (!pixInfo?.qr_code || !pixInfo?.qr_code_base64) {\n        throw new Error(\"PIX QR Code not generated\");\n      }\n\n      return {\n        id: paymentResponse.id.toString(),\n        qrCode: pixInfo.qr_code,\n        qrCodeUrl: `data:image/png;base64,${pixInfo.qr_code_base64}`,\n        pixKey: pixInfo.qr_code,\n        amount: req.amount,\n        transactionAmount: paymentResponse.transaction_amount || req.amount,\n        description: paymentResponse.description || `SmartV IPTV - Plano ${req.planType}`,\n        expiresAt: paymentResponse.date_of_expiration || new Date(Date.now() + 15 * 60 * 1000).toISOString(),\n        status: paymentResponse.status || 'pending'\n      };\n\n    } catch (err) {\n      console.error('Error in createPix:', err);\n      const errorMessage = err instanceof Error ? err.message : String(err);\n      throw APIError.internal(\"Failed to create PIX payment with Mercado Pago: \" + errorMessage);\n    }\n  }\n);\n\n// Function to get Mercado Pago Access Token with fallback\nfunction getMercadoPagoAccessToken(): string {\n  try {\n    const token = mercadoPagoAccessToken();\n    if (token && token.trim()) {\n      return token.trim();\n    }\n  } catch (error) {\n    console.log('Secret MercadoPagoAccessToken not found, using fallback');\n  }\n  \n  // Fallback to the provided test token\n  return 'TEST-3497649211683628-100816-c579d6f751942537a6d13fddbefd9a61-1022896072';\n}\n\nfunction validateDocument(document: string): boolean {\n  if (document.length === 11) {\n    return validateCPF(document);\n  } else if (document.length === 14) {\n    return validateCNPJ(document);\n  }\n  return false;\n}\n\nfunction validateCPF(cpf: string): boolean {\n  if (cpf.length !== 11 || /^(\\d)\\1{10}$/.test(cpf)) return false;\n  \n  let sum = 0;\n  for (let i = 0; i < 9; i++) {\n    sum += parseInt(cpf[i]) * (10 - i);\n  }\n  let digit1 = (sum * 10) % 11;\n  if (digit1 === 10) digit1 = 0;\n  \n  sum = 0;\n  for (let i = 0; i < 10; i++) {\n    sum += parseInt(cpf[i]) * (11 - i);\n  }\n  let digit2 = (sum * 10) % 11;\n  if (digit2 === 10) digit2 = 0;\n  \n  return parseInt(cpf[9]) === digit1 && parseInt(cpf[10]) === digit2;\n}\n\nfunction validateCNPJ(cnpj: string): boolean {\n  if (cnpj.length !== 14 || /^(\\d)\\1{13}$/.test(cnpj)) return false;\n  \n  const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];\n  const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];\n  \n  let sum = 0;\n  for (let i = 0; i < 12; i++) {\n    sum += parseInt(cnpj[i]) * weights1[i];\n  }\n  const digit1 = (sum % 11) < 2 ? 0 : 11 - (sum % 11);\n  \n  sum = 0;\n  for (let i = 0; i < 13; i++) {\n    sum += parseInt(cnpj[i]) * weights2[i];\n  }\n  const digit2 = (sum % 11) < 2 ? 0 : 11 - (sum % 11);\n  \n  return parseInt(cnpj[12]) === digit1 && parseInt(cnpj[13]) === digit2;\n}\n", "import { api, APIError } from \"encore.dev/api\";\n\nexport interface TestRequest {\n  name: string;\n  email: string;\n  plan: string;\n}\n\nexport interface TestResponse {\n  success: boolean;\n  message: string;\n  testUrl: string;\n  credentials: {\n    username: string;\n    password: string;\n  };\n  accessDetails?: {\n    code?: string;\n    dnsStb?: string;\n    urlXciptv?: string[];\n    linkM3u?: string;\n    linkM3uShort?: string;\n    linkHls?: string;\n    linkHlsShort?: string;\n    linkSsiptv?: string;\n    webPlayers?: string[];\n    iptvStream?: string;\n    expiresAt?: string;\n    connections?: number;\n    planName?: string;\n    price?: string;\n    createdAt?: string;\n    renewalUrl?: string;\n  };\n  rawResponse?: string;\n}\n\n// Provides test access to TV channels\nexport const requestTest = api<TestRequest, TestResponse>(\n  { expose: true, method: \"POST\", path: \"/test/request\" },\n  async (req) => {\n    try {\n      // Call the external API to create test account\n      const response = await fetch('https://loginvisionus.com/api/chatbot/rlKWOgOWzo/XYgD9EJDr6', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'User-Agent': 'SmartV-TestBot/1.0',\n        },\n        body: JSON.stringify({ \n          name: req.name, \n          email: req.email \n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const data = await response.text();\n      \n      // Parse the response to extract credentials and details\n      const parsedData = parseTestResponse(data);\n      \n      if (!parsedData.success) {\n        // If parsing fails, still return the raw response for debugging\n        return {\n          success: true,\n          message: \"Teste liberado! Verifique os detalhes abaixo.\",\n          testUrl: \"https://loginvisionus.com/api/chatbot/rlKWOgOWzo/7V01pzaDdO\",\n          credentials: {\n            username: `test_${Date.now()}`,\n            password: Math.random().toString(36).substring(2, 15)\n          },\n          rawResponse: data\n        };\n      }\n\n      return {\n        success: true,\n        message: \"Teste liberado com sucesso! Acesso válido por 4 horas.\",\n        testUrl: \"https://loginvisionus.com/api/chatbot/rlKWOgOWzo/7V01pzaDdO\",\n        credentials: {\n          username: parsedData.username,\n          password: parsedData.password\n        },\n        accessDetails: {\n          code: parsedData.code,\n          dnsStb: parsedData.dnsStb,\n          urlXciptv: parsedData.urlXciptv,\n          linkM3u: parsedData.linkM3u,\n          linkM3uShort: parsedData.linkM3uShort,\n          linkHls: parsedData.linkHls,\n          linkHlsShort: parsedData.linkHlsShort,\n          linkSsiptv: parsedData.linkSsiptv,\n          webPlayers: parsedData.webPlayers,\n          iptvStream: parsedData.iptvStream,\n          expiresAt: parsedData.expiresAt,\n          connections: parsedData.connections,\n          planName: parsedData.planName,\n          price: parsedData.price,\n          createdAt: parsedData.createdAt,\n          renewalUrl: parsedData.renewalUrl\n        },\n        rawResponse: data\n      };\n\n    } catch (error) {\n      console.error('Error calling external test API:', error);\n      \n      // Fallback to local test generation if external API fails\n      const testUsername = `test_${Date.now()}`;\n      const testPassword = Math.random().toString(36).substring(2, 15);\n\n      return {\n        success: true,\n        message: \"Teste liberado com sucesso! Acesso válido por 4 horas (modo local).\",\n        testUrl: \"https://loginvisionus.com/api/chatbot/rlKWOgOWzo/XYgD9EJDr6\",\n        credentials: {\n          username: testUsername,\n          password: testPassword\n        }\n      };\n    }\n  }\n);\n\nfunction parseTestResponse(responseText: string): any {\n  try {\n    // Log the response for debugging\n    console.log('Raw API Response:', responseText);\n\n    // Try to parse as JSON first\n    try {\n      const jsonData = JSON.parse(responseText);\n      \n      // Check if it's the new JSON format\n      if (jsonData.username && jsonData.password) {\n        return {\n          success: true,\n          username: jsonData.username,\n          password: jsonData.password,\n          code: extractCodeFromReply(jsonData.reply || ''),\n          dnsStb: jsonData.dns ? extractDnsFromUrl(jsonData.dns) : null,\n          urlXciptv: extractXciptvUrls(jsonData.reply || ''),\n          linkM3u: extractM3uLink(jsonData.reply || ''),\n          linkM3uShort: extractM3uShortLink(jsonData.reply || ''),\n          linkHls: extractHlsLink(jsonData.reply || ''),\n          linkHlsShort: extractHlsShortLink(jsonData.reply || ''),\n          linkSsiptv: extractSsiptvLink(jsonData.reply || ''),\n          webPlayers: extractWebPlayers(jsonData.reply || ''),\n          iptvStream: extractIptvStream(jsonData.reply || ''),\n          expiresAt: jsonData.expiresAtFormatted || jsonData.expiresAt,\n          connections: jsonData.connections,\n          planName: jsonData.package,\n          price: extractPrice(jsonData.reply || ''),\n          createdAt: jsonData.createdAtFormatted || jsonData.createdAt,\n          renewalUrl: jsonData.payUrl\n        };\n      }\n      \n      // Check if it has a data array with message\n      if (jsonData.data && Array.isArray(jsonData.data) && jsonData.data[0]?.message) {\n        const message = jsonData.data[0].message;\n        return parseTextResponse(message);\n      }\n      \n      // Check if it has a reply field\n      if (jsonData.reply) {\n        return parseTextResponse(jsonData.reply);\n      }\n      \n    } catch (jsonError) {\n      // Not JSON, continue with text parsing\n    }\n\n    // Parse as text\n    return parseTextResponse(responseText);\n\n  } catch (error) {\n    console.error('Error parsing test response:', error);\n    return { success: false };\n  }\n}\n\nfunction parseTextResponse(text: string): any {\n  // Extract username - try multiple patterns\n  let usernameMatch = text.match(/✅\\s*\\*?Usuário\\*?:\\s*(\\d+)/i);\n  if (!usernameMatch) {\n    usernameMatch = text.match(/Usuario:\\s*(\\d+)/i);\n  }\n  if (!usernameMatch) {\n    usernameMatch = text.match(/User:\\s*(\\d+)/i);\n  }\n  if (!usernameMatch) {\n    usernameMatch = text.match(/username[:\\s]*(\\d+)/i);\n  }\n  const username = usernameMatch ? usernameMatch[1] : null;\n\n  // Extract password - try multiple patterns\n  let passwordMatch = text.match(/✅\\s*\\*?Senha\\*?:\\s*(\\d+)/i);\n  if (!passwordMatch) {\n    passwordMatch = text.match(/Senha:\\s*(\\d+)/i);\n  }\n  if (!passwordMatch) {\n    passwordMatch = text.match(/Password:\\s*(\\d+)/i);\n  }\n  if (!passwordMatch) {\n    passwordMatch = text.match(/password[:\\s]*(\\d+)/i);\n  }\n  const password = passwordMatch ? passwordMatch[1] : null;\n\n  // Extract CODE\n  const code = extractCodeFromReply(text);\n\n  // Extract DNS STB\n  let dnsMatch = text.match(/📺\\s*\\*?DNS\\s*STB[\\/\\\\]?SmartUp:?V?3?\\*?\\s*([\\d.]+)/i);\n  if (!dnsMatch) {\n    dnsMatch = text.match(/DNS[:\\s]*([\\d.]+)/i);\n  }\n  const dnsStb = dnsMatch ? dnsMatch[1] : null;\n\n  // Extract XCIPTV URLs\n  const urlXciptv = extractXciptvUrls(text);\n\n  // Extract M3U links\n  const linkM3u = extractM3uLink(text);\n  const linkM3uShort = extractM3uShortLink(text);\n\n  // Extract HLS links\n  const linkHls = extractHlsLink(text);\n  const linkHlsShort = extractHlsShortLink(text);\n\n  // Extract SSIPTV link\n  const linkSsiptv = extractSsiptvLink(text);\n\n  // Extract web players\n  const webPlayers = extractWebPlayers(text);\n\n  // Extract IPTV Stream\n  const iptvStream = extractIptvStream(text);\n\n  // Extract expiration date\n  let expirationMatch = text.match(/🗓️\\s*\\*?Vencimento\\*?:\\s*([^\\n\\r*]+)/i);\n  if (!expirationMatch) {\n    expirationMatch = text.match(/Vencimento[:\\s]*([^\\n\\r*]+)/i);\n  }\n  if (!expirationMatch) {\n    expirationMatch = text.match(/Expira[:\\s]*([^\\n\\r*]+)/i);\n  }\n  const expiresAt = expirationMatch ? expirationMatch[1].trim().replace(/\\*/g, '') : null;\n\n  // Extract connections\n  let connectionsMatch = text.match(/📶\\s*\\*?Conexões\\*?:\\s*(\\d+)/i);\n  if (!connectionsMatch) {\n    connectionsMatch = text.match(/Conexões[:\\s]*(\\d+)/i);\n  }\n  if (!connectionsMatch) {\n    connectionsMatch = text.match(/Connections[:\\s]*(\\d+)/i);\n  }\n  const connections = connectionsMatch ? parseInt(connectionsMatch[1]) : null;\n\n  // Extract plan name\n  let planMatch = text.match(/📦\\s*\\*?Plano\\*?:\\s*([^\\n\\r*]+)/i);\n  if (!planMatch) {\n    planMatch = text.match(/Plano[:\\s]*([^\\n\\r*]+)/i);\n  }\n  if (!planMatch) {\n    planMatch = text.match(/Plan[:\\s]*([^\\n\\r*]+)/i);\n  }\n  const planName = planMatch ? planMatch[1].trim().replace(/\\*/g, '') : null;\n\n  // Extract price\n  const price = extractPrice(text);\n\n  // Extract creation date\n  let createdMatch = text.match(/🗓️\\s*\\*?Criado\\s*em\\*?:\\s*([^\\n\\r*]+)/i);\n  if (!createdMatch) {\n    createdMatch = text.match(/Criado[:\\s]*([^\\n\\r*]+)/i);\n  }\n  if (!createdMatch) {\n    createdMatch = text.match(/Created[:\\s]*([^\\n\\r*]+)/i);\n  }\n  const createdAt = createdMatch ? createdMatch[1].trim().replace(/\\*/g, '') : null;\n\n  // Extract renewal URL\n  let renewalMatch = text.match(/💳\\s*\\*?Assinar[\\/\\\\]?Renovar\\s*Plano\\*?:\\s*(https?:\\/\\/[^\\s\\n*]+)/i);\n  if (!renewalMatch) {\n    renewalMatch = text.match(/Renovar[:\\s]*(https?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  if (!renewalMatch) {\n    renewalMatch = text.match(/Renewal[:\\s]*(https?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  const renewalUrl = renewalMatch ? renewalMatch[1].replace(/\\*/g, '') : null;\n\n  // Check if we have at least username and password\n  if (!username || !password) {\n    console.log('Failed to extract username/password from response');\n    return { success: false };\n  }\n\n  return {\n    success: true,\n    username,\n    password,\n    code,\n    dnsStb,\n    urlXciptv,\n    linkM3u,\n    linkM3uShort,\n    linkHls,\n    linkHlsShort,\n    linkSsiptv,\n    webPlayers,\n    iptvStream,\n    expiresAt,\n    connections,\n    planName,\n    price,\n    createdAt,\n    renewalUrl\n  };\n}\n\nfunction extractCodeFromReply(text: string): string | null {\n  let codeMatch = text.match(/📌\\s*\\*?CODE\\s*\\*?\\s*:\\s*(\\d+)/i);\n  if (!codeMatch) {\n    codeMatch = text.match(/CODE[:\\s]*(\\d+)/i);\n  }\n  return codeMatch ? codeMatch[1] : null;\n}\n\nfunction extractDnsFromUrl(url: string): string | null {\n  // Extract IP from URL like \"http://cs.tvapp.shop:80\"\n  const match = url.match(/https?:\\/\\/([^:\\/\\s]+)/);\n  return match ? match[1] : null;\n}\n\nfunction extractXciptvUrls(text: string): string[] {\n  const xciptvMatches = text.match(/🟠\\s*\\*?URL\\s*XCIPTV\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/gi);\n  return xciptvMatches ? xciptvMatches.map(match => {\n    const urlMatch = match.match(/(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n    return urlMatch ? urlMatch[1].replace(/\\*/g, '') : '';\n  }).filter(url => url) : [];\n}\n\nfunction extractM3uLink(text: string): string | null {\n  let m3uMatch = text.match(/🟢\\s*\\*?Link\\s*\\(M3U\\)\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  if (!m3uMatch) {\n    m3uMatch = text.match(/M3U[:\\s]*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return m3uMatch ? m3uMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractM3uShortLink(text: string): string | null {\n  let m3uShortMatch = text.match(/🟢\\s*\\*?Link\\s*Curto\\s*\\(M3U\\)\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  if (!m3uShortMatch) {\n    m3uShortMatch = text.match(/Link\\s*Curto.*M3U[:\\s]*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return m3uShortMatch ? m3uShortMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractHlsLink(text: string): string | null {\n  let hlsMatch = text.match(/🟡\\s*\\*?Link\\s*\\(HLS\\)\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  if (!hlsMatch) {\n    hlsMatch = text.match(/HLS[:\\s]*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return hlsMatch ? hlsMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractHlsShortLink(text: string): string | null {\n  let hlsShortMatch = text.match(/🟡\\s*\\*?Link\\s*Curto\\s*\\(HLS\\)\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  if (!hlsShortMatch) {\n    hlsShortMatch = text.match(/Link\\s*Curto.*HLS[:\\s]*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return hlsShortMatch ? hlsShortMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractSsiptvLink(text: string): string | null {\n  let ssiptvMatch = text.match(/🔴\\s*\\*?Link\\s*\\(SSIPTV\\)\\*?:\\s*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  if (!ssiptvMatch) {\n    ssiptvMatch = text.match(/SSIPTV[:\\s]*(http[s]?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return ssiptvMatch ? ssiptvMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractWebPlayers(text: string): string[] {\n  const webPlayerSection = text.match(/📺\\s*\\*?WEB\\s*PLAYER\\*?:\\s*((?:http[s]?:\\/\\/[^\\s\\n*]+\\s*)+)/i);\n  return webPlayerSection ? \n    webPlayerSection[1].trim().split(/\\s+/).filter(url => url.startsWith('http')).map(url => url.replace(/\\*/g, '')) : [];\n}\n\nfunction extractIptvStream(text: string): string | null {\n  let iptvStreamMatch = text.match(/📺\\s*\\*?IPTV\\s*STREAM\\*?\\s*(https?:\\/\\/[^\\s\\n*]+)/i);\n  if (!iptvStreamMatch) {\n    iptvStreamMatch = text.match(/IPTV\\s*STREAM[:\\s]*(https?:\\/\\/[^\\s\\n*]+)/i);\n  }\n  return iptvStreamMatch ? iptvStreamMatch[1].replace(/\\*/g, '') : null;\n}\n\nfunction extractPrice(text: string): string | null {\n  let priceMatch = text.match(/💵\\s*\\*?Preço\\s*do\\s*Plano\\*?:\\s*([^\\n\\r*]+)/i);\n  if (!priceMatch) {\n    priceMatch = text.match(/Preço[:\\s]*([^\\n\\r*]+)/i);\n  }\n  if (!priceMatch) {\n    priceMatch = text.match(/Price[:\\s]*([^\\n\\r*]+)/i);\n  }\n  return priceMatch ? priceMatch[1].trim().replace(/\\*/g, '') : null;\n}\n", "import { api, APIError } from \"encore.dev/api\";\n\nexport interface SendReceiptRequest {\n  paymentId: string;\n  customerName: string;\n  customerPhone: string;\n  planType: string;\n  amount: number;\n  paidAt: string;\n}\n\nexport interface SendAdminNotificationRequest {\n  paymentId: string;\n  customerName: string;\n  customerPhone: string;\n  planType: string;\n  amount: number;\n  paidAt: string;\n}\n\nexport interface SendReceiptResponse {\n  success: boolean;\n  message: string;\n}\n\n// Sends payment receipt via WhatsApp\nexport const sendReceipt = api<SendReceiptRequest, SendReceiptResponse>(\n  { expose: true, method: \"POST\", path: \"/whatsapp/send-receipt\" },\n  async (req) => {\n    try {\n      // Format the receipt message\n      const receiptMessage = `\n🎉 *PAGAMENTO CONFIRMADO - SmartV*\n\n✅ *Cliente:* ${req.customerName}\n📺 *Plano:* ${req.planType}\n💰 *Valor:* R$ ${req.amount.toFixed(2).replace('.', ',')}\n📅 *Data:* ${new Date(req.paidAt).toLocaleString('pt-BR')}\n🔢 *ID:* ${req.paymentId}\n\nSeu acesso aos canais foi liberado!\nAproveite sua programação! 📺✨\n\n_SmartV - Sua TV inteligente_\n      `.trim();\n\n      // In a real implementation, you would integrate with WhatsApp API\n      // For now, we'll simulate sending to the admin number\n      const adminPhone = \"+5541995056052\";\n\n      // Here you would use a WhatsApp API service like:\n      // - WhatsApp Business API\n      // - Twilio WhatsApp API\n      // - Evolution API\n      // etc.\n\n      console.log(`Sending receipt to ${adminPhone}:`);\n      console.log(receiptMessage);\n\n      return {\n        success: true,\n        message: \"Comprovante enviado via WhatsApp com sucesso!\"\n      };\n\n    } catch (error) {\n      throw APIError.internal(\"Failed to send WhatsApp receipt\", error instanceof Error ? error : new Error(String(error)));\n    }\n  }\n);\n\n// Sends admin notification when payment is confirmed\nexport const sendAdminNotification = api<SendAdminNotificationRequest, SendReceiptResponse>(\n  { expose: true, method: \"POST\", path: \"/whatsapp/send-admin-notification\" },\n  async (req) => {\n    try {\n      // Format the admin notification message\n      const adminMessage = `\n🚨 *NOVO PAGAMENTO RECEBIDO - SmartV* 🚨\n\n💰 *PAGAMENTO CONFIRMADO*\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n👤 *Cliente:* ${req.customerName}\n📱 *WhatsApp:* ${req.customerPhone}\n📺 *Plano:* ${req.planType}\n💵 *Valor:* R$ ${req.amount.toFixed(2).replace('.', ',')}\n📅 *Data/Hora:* ${new Date(req.paidAt).toLocaleString('pt-BR')}\n🔢 *ID Pagamento:* ${req.paymentId}\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n✅ *Status:* PAGO\n🎯 *Ação:* Liberar acesso ao cliente\n\n_Sistema SmartV - Notificação Automática_\n      `.trim();\n\n      const adminPhone = \"+5541995056052\";\n\n      // Log the admin notification\n      console.log(`🚨 ADMIN NOTIFICATION - New payment received:`);\n      console.log(`Customer: ${req.customerName}`);\n      console.log(`Phone: ${req.customerPhone}`);\n      console.log(`Plan: ${req.planType}`);\n      console.log(`Amount: R$ ${req.amount.toFixed(2)}`);\n      console.log(`Payment ID: ${req.paymentId}`);\n      console.log(`Sending to admin: ${adminPhone}`);\n      console.log('Message:', adminMessage);\n\n      // Here you would integrate with WhatsApp API to send to admin\n      // For now, we'll simulate the sending\n\n      return {\n        success: true,\n        message: \"Notificação enviada para administrador via WhatsApp!\"\n      };\n\n    } catch (error) {\n      console.error('Error sending admin notification:', error);\n      throw APIError.internal(\"Failed to send admin notification\", error instanceof Error ? error : new Error(String(error)));\n    }\n  }\n);\n", "import { Service } from \"encore.dev/service\";\n\nexport default new Service(\"payment\");\n", "import { Service } from \"encore.dev/service\";\n\nexport default new Service(\"whatsapp\");\n", "import { Service } from \"encore.dev/service\";\n\nexport default new Service(\"test\");\n"], "mappings": ";;;;;AAAA,SAAS,kBAAkB,kBAAkB,WAAyB;;;ACAtE,SAAS,KAAK,gBAAgB;AAC9B,SAAS,cAAc;AAEvB,IAAM,yBAAyB,OAAO,wBAAwB;AAevD,IAAM,eAAe;AAAA,EAC1B,EAAE,QAAQ,MAAM,QAAQ,OAAO,MAAM,6BAA6B;AAAA,EAClE,OAAO,QAAQ;AACb,QAAI,CAAC,IAAI,WAAW;AAClB,YAAM,SAAS,gBAAgB,wBAAwB;AAAA,IACzD;AAGA,UAAM,cAAc,0BAA0B;AAE9C,QAAI;AAEF,YAAM,WAAW,MAAM,MAAM,2CAA2C,IAAI,SAAS,IAAI;AAAA,QACvF,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,iBAAiB,UAAU,WAAW;AAAA,UACtC,gBAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,YAAI,SAAS,WAAW,KAAK;AAC3B,gBAAM,SAAS,SAAS,mBAAmB;AAAA,QAC7C;AACA,YAAI,SAAS,WAAW,KAAK;AAC3B,gBAAM,SAAS,gBAAgB,8BAA8B;AAAA,QAC/D;AACA,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,cAAM,SAAS,SAAS,QAAQ,SAAS,MAAM,KAAK,SAAS,EAAE;AAAA,MACjE;AAEA,YAAM,kBAAkB,MAAM,SAAS,KAAK;AAE5C,cAAQ,IAAI,4BAA4B,KAAK,UAAU,iBAAiB,MAAM,CAAC,CAAC;AAEhF,aAAO;AAAA,QACL,IAAI,gBAAgB,IAAI,SAAS,KAAK,IAAI;AAAA,QAC1C,QAAQ,gBAAgB,UAAU;AAAA,QAClC,MAAM,gBAAgB,WAAW;AAAA,QACjC,QAAQ,gBAAgB,sBAAsB;AAAA,QAC9C,QAAQ,gBAAgB,iBAAiB;AAAA,MAC3C;AAAA,IAEF,SAAS,KAAK;AACZ,cAAQ,MAAM,kCAAkC,GAAG;AAGnD,YAAM,eAAe,eAAe,QAAQ,IAAI,UAAU,OAAO,GAAG;AACpE,UAAI,aAAa,SAAS,KAAK,KAAK,aAAa,SAAS,WAAW,GAAG;AACtE,cAAM,SAAS,SAAS,mBAAmB;AAAA,MAC7C;AACA,UAAI,aAAa,SAAS,KAAK,KAAK,aAAa,SAAS,cAAc,GAAG;AACzE,cAAM,SAAS,gBAAgB,8BAA8B;AAAA,MAC/D;AAEA,YAAM,SAAS,SAAS,qCAAqC,YAAY;AAAA,IAC3E;AAAA,EACF;AACF;AAGA,SAAS,4BAAoC;AAC3C,MAAI;AACF,UAAM,QAAQ,uBAAuB;AACrC,QAAI,SAAS,MAAM,KAAK,GAAG;AACzB,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,IAAI,yDAAyD;AAAA,EACvE;AAGA,SAAO;AACT;;;AC3FA,SAAS,OAAAA,MAAK,YAAAC,iBAAgB;AAC9B,SAAS,UAAAC,eAAc;AAEvB,IAAMC,0BAAyBD,QAAO,wBAAwB;AAwBvD,IAAM,YAAYF;AAAA,EACvB,EAAE,QAAQ,MAAM,QAAQ,QAAQ,MAAM,eAAe;AAAA,EACrD,OAAO,QAAQ;AAEb,QAAI,CAAC,IAAI,gBAAgB,CAAC,IAAI,iBAAiB,CAAC,IAAI,oBAAoB,CAAC,IAAI,UAAU;AACrF,YAAMC,UAAS,gBAAgB,yBAAyB;AAAA,IAC1D;AAGA,UAAM,WAAW,IAAI,iBAAiB,QAAQ,OAAO,EAAE;AACvD,QAAI,CAAC,iBAAiB,QAAQ,GAAG;AAC/B,YAAMA,UAAS,gBAAgB,yBAAyB;AAAA,IAC1D;AAGA,QAAI,IAAI,UAAU,GAAG;AACnB,YAAMA,UAAS,gBAAgB,gBAAgB;AAAA,IACjD;AAGA,UAAM,cAAcG,2BAA0B;AAE9C,YAAQ,IAAI,qDAAqD,YAAY,UAAU,GAAG,EAAE,IAAI,KAAK;AAErG,QAAI;AAEF,YAAM,cAAc;AAAA,QAClB,oBAAoB,IAAI;AAAA,QACxB,aAAa,uBAAuB,IAAI,QAAQ;AAAA,QAChD,mBAAmB;AAAA,QACnB,OAAO;AAAA,UACL,OAAO,IAAI;AAAA,UACX,YAAY,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC;AAAA,UACzC,WAAW,IAAI,aAAa,MAAM,GAAG,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC;AAAA,UAC1F,gBAAgB;AAAA,YACd,MAAM,SAAS,WAAW,KAAK,QAAQ;AAAA,YACvC,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,oBAAoB,UAAU,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,QACxD,kBAAkB;AAAA,QAClB,oBAAoB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,MACxE;AAEA,cAAQ,IAAI,4CAA4C,KAAK,UAAU,aAAa,MAAM,CAAC,CAAC;AAE5F,YAAM,WAAW,MAAM,MAAM,2CAA2C;AAAA,QACtE,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB,UAAU,WAAW;AAAA,UACtC,qBAAqB,UAAU,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,QAC3D;AAAA,QACA,MAAM,KAAK,UAAU,WAAW;AAAA,MAClC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK;AACtC,gBAAQ,MAAM,2BAA2B,SAAS,QAAQ,SAAS;AACnE,cAAM,IAAI,MAAM,2BAA2B,SAAS,MAAM,MAAM,SAAS,EAAE;AAAA,MAC7E;AAEA,YAAM,kBAAkB,MAAM,SAAS,KAAK;AAE5C,cAAQ,IAAI,8CAA8C,gBAAgB,EAAE;AAC5E,cAAQ,IAAI,qBAAqB,KAAK,UAAU,iBAAiB,MAAM,CAAC,CAAC;AAEzE,UAAI,CAAC,gBAAgB,IAAI;AACvB,cAAM,IAAI,MAAM,uCAAuC;AAAA,MACzD;AAGA,YAAM,UAAU,gBAAgB,sBAAsB;AAEtD,UAAI,CAAC,SAAS,WAAW,CAAC,SAAS,gBAAgB;AACjD,cAAM,IAAI,MAAM,2BAA2B;AAAA,MAC7C;AAEA,aAAO;AAAA,QACL,IAAI,gBAAgB,GAAG,SAAS;AAAA,QAChC,QAAQ,QAAQ;AAAA,QAChB,WAAW,yBAAyB,QAAQ,cAAc;AAAA,QAC1D,QAAQ,QAAQ;AAAA,QAChB,QAAQ,IAAI;AAAA,QACZ,mBAAmB,gBAAgB,sBAAsB,IAAI;AAAA,QAC7D,aAAa,gBAAgB,eAAe,uBAAuB,IAAI,QAAQ;AAAA,QAC/E,WAAW,gBAAgB,sBAAsB,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,GAAI,EAAE,YAAY;AAAA,QACnG,QAAQ,gBAAgB,UAAU;AAAA,MACpC;AAAA,IAEF,SAAS,KAAK;AACZ,cAAQ,MAAM,uBAAuB,GAAG;AACxC,YAAM,eAAe,eAAe,QAAQ,IAAI,UAAU,OAAO,GAAG;AACpE,YAAMH,UAAS,SAAS,qDAAqD,YAAY;AAAA,IAC3F;AAAA,EACF;AACF;AAGA,SAASG,6BAAoC;AAC3C,MAAI;AACF,UAAM,QAAQD,wBAAuB;AACrC,QAAI,SAAS,MAAM,KAAK,GAAG;AACzB,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,IAAI,yDAAyD;AAAA,EACvE;AAGA,SAAO;AACT;AAEA,SAAS,iBAAiB,UAA2B;AACnD,MAAI,SAAS,WAAW,IAAI;AAC1B,WAAO,YAAY,QAAQ;AAAA,EAC7B,WAAW,SAAS,WAAW,IAAI;AACjC,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,SAAO;AACT;AAEA,SAAS,YAAY,KAAsB;AACzC,MAAI,IAAI,WAAW,MAAM,eAAe,KAAK,GAAG;AAAG,WAAO;AAE1D,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAO,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,SAAU,MAAM,KAAM;AAC1B,MAAI,WAAW;AAAI,aAAS;AAE5B,QAAM;AACN,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,WAAO,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK;AAAA,EAClC;AACA,MAAI,SAAU,MAAM,KAAM;AAC1B,MAAI,WAAW;AAAI,aAAS;AAE5B,SAAO,SAAS,IAAI,CAAC,CAAC,MAAM,UAAU,SAAS,IAAI,EAAE,CAAC,MAAM;AAC9D;AAEA,SAAS,aAAa,MAAuB;AAC3C,MAAI,KAAK,WAAW,MAAM,eAAe,KAAK,IAAI;AAAG,WAAO;AAE5D,QAAM,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpD,QAAM,WAAW,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAEvD,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,WAAO,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC;AAAA,EACvC;AACA,QAAM,SAAU,MAAM,KAAM,IAAI,IAAI,KAAM,MAAM;AAEhD,QAAM;AACN,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC3B,WAAO,SAAS,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC;AAAA,EACvC;AACA,QAAM,SAAU,MAAM,KAAM,IAAI,IAAI,KAAM,MAAM;AAEhD,SAAO,SAAS,KAAK,EAAE,CAAC,MAAM,UAAU,SAAS,KAAK,EAAE,CAAC,MAAM;AACjE;;;AC5LA,SAAS,OAAAE,YAAqB;AAsCvB,IAAM,cAAcA;AAAA,EACzB,EAAE,QAAQ,MAAM,QAAQ,QAAQ,MAAM,gBAAgB;AAAA,EACtD,OAAO,QAAQ;AACb,QAAI;AAEF,YAAM,WAAW,MAAM,MAAM,+DAA+D;AAAA,QAC1F,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB;AAAA,QACA,MAAM,KAAK,UAAU;AAAA,UACnB,MAAM,IAAI;AAAA,UACV,OAAO,IAAI;AAAA,QACb,CAAC;AAAA,MACH,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,IAAI,MAAM,QAAQ,SAAS,MAAM,KAAK,SAAS,UAAU,EAAE;AAAA,MACnE;AAEA,YAAM,OAAO,MAAM,SAAS,KAAK;AAGjC,YAAM,aAAa,kBAAkB,IAAI;AAEzC,UAAI,CAAC,WAAW,SAAS;AAEvB,eAAO;AAAA,UACL,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,aAAa;AAAA,YACX,UAAU,QAAQ,KAAK,IAAI,CAAC;AAAA,YAC5B,UAAU,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAAA,UACtD;AAAA,UACA,aAAa;AAAA,QACf;AAAA,MACF;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,aAAa;AAAA,UACX,UAAU,WAAW;AAAA,UACrB,UAAU,WAAW;AAAA,QACvB;AAAA,QACA,eAAe;AAAA,UACb,MAAM,WAAW;AAAA,UACjB,QAAQ,WAAW;AAAA,UACnB,WAAW,WAAW;AAAA,UACtB,SAAS,WAAW;AAAA,UACpB,cAAc,WAAW;AAAA,UACzB,SAAS,WAAW;AAAA,UACpB,cAAc,WAAW;AAAA,UACzB,YAAY,WAAW;AAAA,UACvB,YAAY,WAAW;AAAA,UACvB,YAAY,WAAW;AAAA,UACvB,WAAW,WAAW;AAAA,UACtB,aAAa,WAAW;AAAA,UACxB,UAAU,WAAW;AAAA,UACrB,OAAO,WAAW;AAAA,UAClB,WAAW,WAAW;AAAA,UACtB,YAAY,WAAW;AAAA,QACzB;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,oCAAoC,KAAK;AAGvD,YAAM,eAAe,QAAQ,KAAK,IAAI,CAAC;AACvC,YAAM,eAAe,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,GAAG,EAAE;AAE/D,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,aAAa;AAAA,UACX,UAAU;AAAA,UACV,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAkB,cAA2B;AACpD,MAAI;AAEF,YAAQ,IAAI,qBAAqB,YAAY;AAG7C,QAAI;AACF,YAAM,WAAW,KAAK,MAAM,YAAY;AAGxC,UAAI,SAAS,YAAY,SAAS,UAAU;AAC1C,eAAO;AAAA,UACL,SAAS;AAAA,UACT,UAAU,SAAS;AAAA,UACnB,UAAU,SAAS;AAAA,UACnB,MAAM,qBAAqB,SAAS,SAAS,EAAE;AAAA,UAC/C,QAAQ,SAAS,MAAM,kBAAkB,SAAS,GAAG,IAAI;AAAA,UACzD,WAAW,kBAAkB,SAAS,SAAS,EAAE;AAAA,UACjD,SAAS,eAAe,SAAS,SAAS,EAAE;AAAA,UAC5C,cAAc,oBAAoB,SAAS,SAAS,EAAE;AAAA,UACtD,SAAS,eAAe,SAAS,SAAS,EAAE;AAAA,UAC5C,cAAc,oBAAoB,SAAS,SAAS,EAAE;AAAA,UACtD,YAAY,kBAAkB,SAAS,SAAS,EAAE;AAAA,UAClD,YAAY,kBAAkB,SAAS,SAAS,EAAE;AAAA,UAClD,YAAY,kBAAkB,SAAS,SAAS,EAAE;AAAA,UAClD,WAAW,SAAS,sBAAsB,SAAS;AAAA,UACnD,aAAa,SAAS;AAAA,UACtB,UAAU,SAAS;AAAA,UACnB,OAAO,aAAa,SAAS,SAAS,EAAE;AAAA,UACxC,WAAW,SAAS,sBAAsB,SAAS;AAAA,UACnD,YAAY,SAAS;AAAA,QACvB;AAAA,MACF;AAGA,UAAI,SAAS,QAAQ,MAAM,QAAQ,SAAS,IAAI,KAAK,SAAS,KAAK,CAAC,GAAG,SAAS;AAC9E,cAAM,UAAU,SAAS,KAAK,CAAC,EAAE;AACjC,eAAO,kBAAkB,OAAO;AAAA,MAClC;AAGA,UAAI,SAAS,OAAO;AAClB,eAAO,kBAAkB,SAAS,KAAK;AAAA,MACzC;AAAA,IAEF,SAAS,WAAW;AAAA,IAEpB;AAGA,WAAO,kBAAkB,YAAY;AAAA,EAEvC,SAAS,OAAO;AACd,YAAQ,MAAM,gCAAgC,KAAK;AACnD,WAAO,EAAE,SAAS,MAAM;AAAA,EAC1B;AACF;AAEA,SAAS,kBAAkB,MAAmB;AAE5C,MAAI,gBAAgB,KAAK,MAAM,6BAA6B;AAC5D,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,mBAAmB;AAAA,EAChD;AACA,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,gBAAgB;AAAA,EAC7C;AACA,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,sBAAsB;AAAA,EACnD;AACA,QAAM,WAAW,gBAAgB,cAAc,CAAC,IAAI;AAGpD,MAAI,gBAAgB,KAAK,MAAM,2BAA2B;AAC1D,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,iBAAiB;AAAA,EAC9C;AACA,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,oBAAoB;AAAA,EACjD;AACA,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,sBAAsB;AAAA,EACnD;AACA,QAAM,WAAW,gBAAgB,cAAc,CAAC,IAAI;AAGpD,QAAM,OAAO,qBAAqB,IAAI;AAGtC,MAAI,WAAW,KAAK,MAAM,sDAAsD;AAChF,MAAI,CAAC,UAAU;AACb,eAAW,KAAK,MAAM,oBAAoB;AAAA,EAC5C;AACA,QAAM,SAAS,WAAW,SAAS,CAAC,IAAI;AAGxC,QAAM,YAAY,kBAAkB,IAAI;AAGxC,QAAM,UAAU,eAAe,IAAI;AACnC,QAAM,eAAe,oBAAoB,IAAI;AAG7C,QAAM,UAAU,eAAe,IAAI;AACnC,QAAM,eAAe,oBAAoB,IAAI;AAG7C,QAAM,aAAa,kBAAkB,IAAI;AAGzC,QAAM,aAAa,kBAAkB,IAAI;AAGzC,QAAM,aAAa,kBAAkB,IAAI;AAGzC,MAAI,kBAAkB,KAAK,MAAM,wCAAwC;AACzE,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,KAAK,MAAM,8BAA8B;AAAA,EAC7D;AACA,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,KAAK,MAAM,0BAA0B;AAAA,EACzD;AACA,QAAM,YAAY,kBAAkB,gBAAgB,CAAC,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,IAAI;AAGnF,MAAI,mBAAmB,KAAK,MAAM,+BAA+B;AACjE,MAAI,CAAC,kBAAkB;AACrB,uBAAmB,KAAK,MAAM,sBAAsB;AAAA,EACtD;AACA,MAAI,CAAC,kBAAkB;AACrB,uBAAmB,KAAK,MAAM,yBAAyB;AAAA,EACzD;AACA,QAAM,cAAc,mBAAmB,SAAS,iBAAiB,CAAC,CAAC,IAAI;AAGvE,MAAI,YAAY,KAAK,MAAM,kCAAkC;AAC7D,MAAI,CAAC,WAAW;AACd,gBAAY,KAAK,MAAM,yBAAyB;AAAA,EAClD;AACA,MAAI,CAAC,WAAW;AACd,gBAAY,KAAK,MAAM,wBAAwB;AAAA,EACjD;AACA,QAAM,WAAW,YAAY,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,IAAI;AAGtE,QAAM,QAAQ,aAAa,IAAI;AAG/B,MAAI,eAAe,KAAK,MAAM,yCAAyC;AACvE,MAAI,CAAC,cAAc;AACjB,mBAAe,KAAK,MAAM,0BAA0B;AAAA,EACtD;AACA,MAAI,CAAC,cAAc;AACjB,mBAAe,KAAK,MAAM,2BAA2B;AAAA,EACvD;AACA,QAAM,YAAY,eAAe,aAAa,CAAC,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,IAAI;AAG7E,MAAI,eAAe,KAAK,MAAM,qEAAqE;AACnG,MAAI,CAAC,cAAc;AACjB,mBAAe,KAAK,MAAM,sCAAsC;AAAA,EAClE;AACA,MAAI,CAAC,cAAc;AACjB,mBAAe,KAAK,MAAM,sCAAsC;AAAA,EAClE;AACA,QAAM,aAAa,eAAe,aAAa,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AAGvE,MAAI,CAAC,YAAY,CAAC,UAAU;AAC1B,YAAQ,IAAI,mDAAmD;AAC/D,WAAO,EAAE,SAAS,MAAM;AAAA,EAC1B;AAEA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,qBAAqB,MAA6B;AACzD,MAAI,YAAY,KAAK,MAAM,iCAAiC;AAC5D,MAAI,CAAC,WAAW;AACd,gBAAY,KAAK,MAAM,kBAAkB;AAAA,EAC3C;AACA,SAAO,YAAY,UAAU,CAAC,IAAI;AACpC;AAEA,SAAS,kBAAkB,KAA4B;AAErD,QAAM,QAAQ,IAAI,MAAM,wBAAwB;AAChD,SAAO,QAAQ,MAAM,CAAC,IAAI;AAC5B;AAEA,SAAS,kBAAkB,MAAwB;AACjD,QAAM,gBAAgB,KAAK,MAAM,uDAAuD;AACxF,SAAO,gBAAgB,cAAc,IAAI,WAAS;AAChD,UAAM,WAAW,MAAM,MAAM,2BAA2B;AACxD,WAAO,WAAW,SAAS,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AAAA,EACrD,CAAC,EAAE,OAAO,SAAO,GAAG,IAAI,CAAC;AAC3B;AAEA,SAAS,eAAe,MAA6B;AACnD,MAAI,WAAW,KAAK,MAAM,wDAAwD;AAClF,MAAI,CAAC,UAAU;AACb,eAAW,KAAK,MAAM,oCAAoC;AAAA,EAC5D;AACA,SAAO,WAAW,SAAS,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AACrD;AAEA,SAAS,oBAAoB,MAA6B;AACxD,MAAI,gBAAgB,KAAK,MAAM,gEAAgE;AAC/F,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,kDAAkD;AAAA,EAC/E;AACA,SAAO,gBAAgB,cAAc,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AAC/D;AAEA,SAAS,eAAe,MAA6B;AACnD,MAAI,WAAW,KAAK,MAAM,wDAAwD;AAClF,MAAI,CAAC,UAAU;AACb,eAAW,KAAK,MAAM,oCAAoC;AAAA,EAC5D;AACA,SAAO,WAAW,SAAS,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AACrD;AAEA,SAAS,oBAAoB,MAA6B;AACxD,MAAI,gBAAgB,KAAK,MAAM,gEAAgE;AAC/F,MAAI,CAAC,eAAe;AAClB,oBAAgB,KAAK,MAAM,kDAAkD;AAAA,EAC/E;AACA,SAAO,gBAAgB,cAAc,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AAC/D;AAEA,SAAS,kBAAkB,MAA6B;AACtD,MAAI,cAAc,KAAK,MAAM,2DAA2D;AACxF,MAAI,CAAC,aAAa;AAChB,kBAAc,KAAK,MAAM,uCAAuC;AAAA,EAClE;AACA,SAAO,cAAc,YAAY,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AAC3D;AAEA,SAAS,kBAAkB,MAAwB;AACjD,QAAM,mBAAmB,KAAK,MAAM,8DAA8D;AAClG,SAAO,mBACL,iBAAiB,CAAC,EAAE,KAAK,EAAE,MAAM,KAAK,EAAE,OAAO,SAAO,IAAI,WAAW,MAAM,CAAC,EAAE,IAAI,SAAO,IAAI,QAAQ,OAAO,EAAE,CAAC,IAAI,CAAC;AACxH;AAEA,SAAS,kBAAkB,MAA6B;AACtD,MAAI,kBAAkB,KAAK,MAAM,oDAAoD;AACrF,MAAI,CAAC,iBAAiB;AACpB,sBAAkB,KAAK,MAAM,4CAA4C;AAAA,EAC3E;AACA,SAAO,kBAAkB,gBAAgB,CAAC,EAAE,QAAQ,OAAO,EAAE,IAAI;AACnE;AAEA,SAAS,aAAa,MAA6B;AACjD,MAAI,aAAa,KAAK,MAAM,+CAA+C;AAC3E,MAAI,CAAC,YAAY;AACf,iBAAa,KAAK,MAAM,yBAAyB;AAAA,EACnD;AACA,MAAI,CAAC,YAAY;AACf,iBAAa,KAAK,MAAM,yBAAyB;AAAA,EACnD;AACA,SAAO,aAAa,WAAW,CAAC,EAAE,KAAK,EAAE,QAAQ,OAAO,EAAE,IAAI;AAChE;;;ACzZA,SAAS,OAAAC,MAAK,YAAAC,iBAAgB;AA0BvB,IAAM,cAAcD;AAAA,EACzB,EAAE,QAAQ,MAAM,QAAQ,QAAQ,MAAM,yBAAyB;AAAA,EAC/D,OAAO,QAAQ;AACb,QAAI;AAEF,YAAM,iBAAiB;AAAA;AAAA;AAAA,eAGd,IAAI,YAAY;AAAA,cACjB,IAAI,QAAQ;AAAA,iBACT,IAAI,OAAO,QAAQ,CAAC,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,aAC3C,IAAI,KAAK,IAAI,MAAM,EAAE,eAAe,OAAO,CAAC;AAAA,WAC9C,IAAI,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMhB,KAAK;AAIP,YAAM,aAAa;AAQnB,cAAQ,IAAI,sBAAsB,UAAU,GAAG;AAC/C,cAAQ,IAAI,cAAc;AAE1B,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IAEF,SAAS,OAAO;AACd,YAAMC,UAAS,SAAS,mCAAmC,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,IACtH;AAAA,EACF;AACF;AAGO,IAAM,wBAAwBD;AAAA,EACnC,EAAE,QAAQ,MAAM,QAAQ,QAAQ,MAAM,oCAAoC;AAAA,EAC1E,OAAO,QAAQ;AACb,QAAI;AAEF,YAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAMX,IAAI,YAAY;AAAA,iBACf,IAAI,aAAa;AAAA,cACpB,IAAI,QAAQ;AAAA,iBACT,IAAI,OAAO,QAAQ,CAAC,EAAE,QAAQ,KAAK,GAAG,CAAC;AAAA,kBACtC,IAAI,KAAK,IAAI,MAAM,EAAE,eAAe,OAAO,CAAC;AAAA,qBACzC,IAAI,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAO1B,KAAK;AAEP,YAAM,aAAa;AAGnB,cAAQ,IAAI,+CAA+C;AAC3D,cAAQ,IAAI,aAAa,IAAI,YAAY,EAAE;AAC3C,cAAQ,IAAI,UAAU,IAAI,aAAa,EAAE;AACzC,cAAQ,IAAI,SAAS,IAAI,QAAQ,EAAE;AACnC,cAAQ,IAAI,cAAc,IAAI,OAAO,QAAQ,CAAC,CAAC,EAAE;AACjD,cAAQ,IAAI,eAAe,IAAI,SAAS,EAAE;AAC1C,cAAQ,IAAI,qBAAqB,UAAU,EAAE;AAC7C,cAAQ,IAAI,YAAY,YAAY;AAKpC,aAAO;AAAA,QACL,SAAS;AAAA,QACT,SAAS;AAAA,MACX;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AACxD,YAAMC,UAAS,SAAS,qCAAqC,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,IACxH;AAAA,EACF;AACF;;;ACzHA,SAAS,eAAe;AAExB,IAAO,yBAAQ,IAAI,QAAQ,SAAS;;;ACFpC,SAAS,WAAAC,gBAAe;AAExB,IAAOC,0BAAQ,IAAID,SAAQ,UAAU;;;ACFrC,SAAS,WAAAE,gBAAe;AAExB,IAAOC,0BAAQ,IAAID,SAAQ,MAAM;;;APUjC,IAAM,WAAkB,CACxB;AAEA,IAAM,WAAsB;AAAA,EACxB;AAAA,IACI,UAAU;AAAA,MACN,SAAmB;AAAA,MACnB,MAAmB;AAAA,MACnB,SAAmB;AAAA,MACnB,KAAmB;AAAA,MACnB,kBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,IACA,iBAAiB,EAAC,UAAS,MAAK,QAAO,OAAM,SAAQ,OAAM,YAAW,OAAM,QAAO,CAAC,EAAC;AAAA,IACrF,aAA6B,uBAAQ,IAAI,eAAe,CAAC;AAAA,EAC7D;AAAA,EACA;AAAA,IACI,UAAU;AAAA,MACN,SAAmB;AAAA,MACnB,MAAmB;AAAA,MACnB,SAAmB;AAAA,MACnB,KAAmB;AAAA,MACnB,kBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,IACA,iBAAiB,EAAC,UAAS,MAAK,QAAO,OAAM,SAAQ,OAAM,YAAW,OAAM,QAAO,CAAC,EAAC;AAAA,IACrF,aAA6B,uBAAQ,IAAI,eAAe,CAAC;AAAA,EAC7D;AAAA,EACA;AAAA,IACI,UAAU;AAAA,MACN,SAAmB;AAAA,MACnB,MAAmB;AAAA,MACnB,SAAmB;AAAA,MACnB,KAAmB;AAAA,MACnB,kBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,IACA,iBAAiB,EAAC,UAAS,MAAK,QAAO,OAAM,SAAQ,OAAM,YAAW,OAAM,QAAO,CAAC,EAAC;AAAA,IACrF,aAA0BE,wBAAQ,IAAI,eAAe,CAAC;AAAA,EAC1D;AAAA,EACA;AAAA,IACI,UAAU;AAAA,MACN,SAAmB;AAAA,MACnB,MAAmB;AAAA,MACnB,SAAmB;AAAA,MACnB,KAAmB;AAAA,MACnB,kBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,IACA,iBAAiB,EAAC,UAAS,MAAK,QAAO,OAAM,SAAQ,OAAM,YAAW,OAAM,QAAO,CAAC,EAAC;AAAA,IACrF,aAA8BA,wBAAQ,IAAI,eAAe,CAAC;AAAA,EAC9D;AAAA,EACA;AAAA,IACI,UAAU;AAAA,MACN,SAAmB;AAAA,MACnB,MAAmB;AAAA,MACnB,SAAmB;AAAA,MACnB,KAAmB;AAAA,MACnB,kBAAmB;AAAA,MACnB,mBAAmB;AAAA,IACvB;AAAA,IACA,iBAAiB,EAAC,UAAS,MAAK,QAAO,OAAM,SAAQ,OAAM,YAAW,OAAM,QAAO,CAAC,EAAC;AAAA,IACrF,aAA8BA,wBAAQ,IAAI,eAAe,CAAC;AAAA,EAC9D;AACJ;AAEA,iBAAiB,QAAQ;AACzB,iBAAiB,QAAQ;AAEzB,MAAM,IAAI,YAAY,GAAG;", "names": ["api", "APIError", "secret", "mercadoPagoAccessToken", "getMercadoPagoAccessToken", "api", "api", "APIError", "Service", "encore_service_default", "Service", "encore_service_default", "encore_service_default"]}