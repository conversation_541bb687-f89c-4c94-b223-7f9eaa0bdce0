import { apiCall, streamIn, streamOut, streamInOut } from "encore.dev/internal/codegen/api";
import { registerTestHand<PERSON> } from "encore.dev/internal/codegen/appinit";

import * as test_service from "../../../../test\\encore.service";

export async function requestTest(params, opts) {
    const handler = (await import("../../../../test\\test-api")).requestTest;
    registerTestHandler({
        apiRoute: { service: "test", name: "requestTest", raw: false, handler, streamingRequest: false, streamingResponse: false },
        middlewares: test_service.default.cfg.middlewares || [],
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
    });

    return apiCall("test", "requestTest", params, opts);
}

