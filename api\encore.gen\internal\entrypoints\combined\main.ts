import { registerGateways, registerHandlers, run, type Handler } from "encore.dev/internal/codegen/appinit";

import { checkPayment as payment_checkPaymentImpl0 } from "../../../../payment\\check-payment";
import { createPix as payment_createPixImpl1 } from "../../../../payment\\create-pix";
import { requestTest as test_requestTestImpl2 } from "../../../../test\\test-api";
import { sendReceipt as whatsapp_sendReceiptImpl3 } from "../../../../whatsapp\\send-receipt";
import { sendAdminNotification as whatsapp_sendAdminNotificationImpl4 } from "../../../../whatsapp\\send-receipt";
import * as payment_service from "../../../../payment\\encore.service";
import * as whatsapp_service from "../../../../whatsapp\\encore.service";
import * as frontend_service from "../../../../frontend\\encore.service";
import * as test_service from "../../../../test\\encore.service";

const gateways: any[] = [
];

const handlers: Handler[] = [
    {
        apiRoute: {
            service:           "payment",
            name:              "checkPayment",
            handler:           payment_checkPaymentImpl0,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: payment_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "payment",
            name:              "createPix",
            handler:           payment_createPixImpl1,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: payment_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "test",
            name:              "requestTest",
            handler:           test_requestTestImpl2,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: test_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "whatsapp",
            name:              "sendReceipt",
            handler:           whatsapp_sendReceiptImpl3,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: whatsapp_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "whatsapp",
            name:              "sendAdminNotification",
            handler:           whatsapp_sendAdminNotificationImpl4,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: whatsapp_service.default.cfg.middlewares || [],
    },
];

registerGateways(gateways);
registerHandlers(handlers);

await run(import.meta.url);
