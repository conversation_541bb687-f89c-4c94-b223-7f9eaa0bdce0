import { registerHandlers, run, type Handler } from "encore.dev/internal/codegen/appinit";
import { Worker, isMainThread } from "node:worker_threads";
import { fileURLToPath } from "node:url";
import { availableParallelism } from "node:os";

import { checkPayment as checkPaymentImpl0 } from "../../../../../payment\\check-payment";
import { createPix as createPixImpl1 } from "../../../../../payment\\create-pix";
import * as payment_service from "../../../../../payment\\encore.service";

const handlers: Handler[] = [
    {
        apiRoute: {
            service:           "payment",
            name:              "checkPayment",
            handler:           checkPaymentImpl0,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: payment_service.default.cfg.middlewares || [],
    },
    {
        apiRoute: {
            service:           "payment",
            name:              "createPix",
            handler:           createPixImpl1,
            raw:               false,
            streamingRequest:  false,
            streamingResponse: false,
        },
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
        middlewares: payment_service.default.cfg.middlewares || [],
    },
];

registerHandlers(handlers);

await run(import.meta.url);
