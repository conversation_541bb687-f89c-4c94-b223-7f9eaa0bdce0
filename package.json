{"name": "backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host smartv.shop --port 5173", "build": "vite build", "preview": "vite preview", "start": "npm run dev", "start-full": "concurrently \"npm run dev\" \"cd api && encore run\"", "proxy": "cd api && node proxy-server.cjs"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "encore.dev": "^1.48.8", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "sweetalert2": "^11.7.27", "tailwind-merge": "^1.14.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "concurrently": "^8.2.0", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "typescript": "^5.8.3", "vite": "^4.4.5"}}