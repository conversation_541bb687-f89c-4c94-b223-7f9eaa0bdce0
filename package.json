{"name": "backend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "start": "npm run dev", "start-full": "concurrently \"npm run dev\" \"cd api && encore run\"", "proxy": "cd api && node proxy-server.cjs"}, "dependencies": {"cors": "^2.8.5", "encore.dev": "^1.48.8", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "lucide-react": "^0.263.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "sweetalert2": "^11.7.27"}, "devDependencies": {"typescript": "^5.8.3", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.0", "concurrently": "^8.2.0"}}