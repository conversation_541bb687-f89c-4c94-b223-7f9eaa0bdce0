import { apiCall, streamIn, streamOut, streamInOut } from "encore.dev/internal/codegen/api";
import { registerTestHandler } from "encore.dev/internal/codegen/appinit";

import * as payment_service from "../../../../payment\\encore.service";

export async function checkPayment(params, opts) {
    const handler = (await import("../../../../payment\\check-payment")).checkPayment;
    registerTestHandler({
        apiRoute: { service: "payment", name: "checkPayment", raw: false, handler, streamingRequest: false, streamingResponse: false },
        middlewares: payment_service.default.cfg.middlewares || [],
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
    });

    return apiCall("payment", "checkPayment", params, opts);
}

export async function createPix(params, opts) {
    const handler = (await import("../../../../payment\\create-pix")).createPix;
    registerTestHandler({
        apiRoute: { service: "payment", name: "createPix", raw: false, handler, streamingRequest: false, streamingResponse: false },
        middlewares: payment_service.default.cfg.middlewares || [],
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
    });

    return apiCall("payment", "createPix", params, opts);
}

