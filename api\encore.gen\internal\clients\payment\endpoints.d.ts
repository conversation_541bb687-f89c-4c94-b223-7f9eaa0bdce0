import { CallOpts } from "encore.dev/api";

type Parameters<T> = T extends (...args: infer P) => unknown ? P : never;
type WithCallOpts<T extends (...args: any) => any> = (
  ...args: [...Parameters<T>, opts?: CallOpts]
) => ReturnType<T>;

import { checkPayment as checkPayment_handler } from "../../../../payment\\check-payment.js";
declare const checkPayment: WithCallOpts<typeof checkPayment_handler>;
export { checkPayment };

import { createPix as createPix_handler } from "../../../../payment\\create-pix.js";
declare const createPix: WithCallOpts<typeof createPix_handler>;
export { createPix };


