import { apiCall, streamIn, streamOut, streamInOut } from "encore.dev/internal/codegen/api";
import { registerTestHandler } from "encore.dev/internal/codegen/appinit";

import * as whatsapp_service from "../../../../whatsapp\\encore.service";

export async function sendReceipt(params, opts) {
    const handler = (await import("../../../../whatsapp\\send-receipt")).sendReceipt;
    registerTestHandler({
        apiRoute: { service: "whatsapp", name: "sendReceipt", raw: false, handler, streamingRequest: false, streamingResponse: false },
        middlewares: whatsapp_service.default.cfg.middlewares || [],
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
    });

    return apiCall("whatsapp", "sendReceipt", params, opts);
}

export async function sendAdminNotification(params, opts) {
    const handler = (await import("../../../../whatsapp\\send-receipt")).sendAdminNotification;
    registerTestHandler({
        apiRoute: { service: "whatsapp", name: "sendAdminNotification", raw: false, handler, streamingRequest: false, streamingResponse: false },
        middlewares: whatsapp_service.default.cfg.middlewares || [],
        endpointOptions: {"expose":true,"auth":false,"isRaw":false,"isStream":false,"tags":[]},
    });

    return apiCall("whatsapp", "sendAdminNotification", params, opts);
}

