import { defineConfig } from 'vite'
import path from 'path'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => ({
  resolve: {
    alias: {
      '@': path.resolve(__dirname),
      '~backend/client': path.resolve(__dirname, './client'),
      '~backend': path.resolve(__dirname, './api'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
    strictPort: true,
    allowedHosts: [
      'smartv.shop',
      'www.smartv.shop',
      'localhost',
      '127.0.0.1'
    ],
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  preview: {
    host: '0.0.0.0',
    port: 5173,
    allowedHosts: [
      'smartv.shop',
      'www.smartv.shop',
      'localhost',
      '127.0.0.1'
    ]
  },
  plugins: [
    react({
      jsxRuntime: 'automatic'
    }),
  ],
  css: {
    postcss: './postcss.config.js',
  },
  mode: mode || "production",
  build: {
    minify: mode === 'production',
  },
  define: {
    'import.meta.env.VITE_CLIENT_TARGET': JSON.stringify(
      mode === 'development' ? 'http://localhost:5173/api' : (process.env.VITE_CLIENT_TARGET || 'https://smartv.shop/api')
    ),
  },
}))
